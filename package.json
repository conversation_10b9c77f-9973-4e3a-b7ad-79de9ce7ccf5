{"name": "luc-system-vue", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit && vite build --mode production", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "start": "pnpm run dev"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.8.2", "axios": "^1.8.2", "dayjs": "^1.11.13", "element-plus": "^2.9.6", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.10", "@vitejs/plugin-vue": "^5.2.1", "element-plus": "^2.9.6", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "unplugin-element-plus": "^0.9.1", "vite": "^6.2.1", "vue-tsc": "2.1.10"}}