/**
 * 应用常量
 */

/**
 * 登录页面路径
 */
export const LOGIN_PATH = '/auth/login'

/**
 * 默认首页路径
 */
export const DEFAULT_HOME_PATH = '/dashboard'

/**
 * 存储键名
 */
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  PERMISSIONS: 'permissions',
  ROLES: 'roles',
  PREFERENCES: 'preferences',
  LOCALE: 'locale',
  THEME: 'theme',
  PKCE_STATE: 'pkce_state',
  SESSION_TOKEN: 'session_token',
} as const

/**
 * 支持的语言
 */
export interface LanguageOption {
  label: string
  value: 'en-US' | 'zh-CN'
}

export const SUPPORT_LANGUAGES: LanguageOption[] = [
  {
    label: '简体中文',
    value: 'zh-CN',
  },
  {
    label: 'English',
    value: 'en-US',
  },
]

/**
 * 主题模式
 */
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto',
} as const

/**
 * 布局模式
 */
export const LAYOUT_MODES = {
  SIDEBAR: 'sidebar',
  TOP_MENU: 'top-menu',
  MIX: 'mix',
} as const

/**
 * 权限模式
 */
export const ACCESS_MODES = {
  FRONTEND: 'frontend',
  BACKEND: 'backend',
} as const

/**
 * CSS 变量名
 */
export const CSS_VARIABLES = {
  LAYOUT_CONTENT_HEIGHT: '--luc-content-height',
  LAYOUT_CONTENT_WIDTH: '--luc-content-width',
  LAYOUT_HEADER_HEIGHT: '--luc-header-height',
  LAYOUT_FOOTER_HEIGHT: '--luc-footer-height',
  LAYOUT_SIDEBAR_WIDTH: '--luc-sidebar-width',
} as const

/**
 * 元素 ID
 */
export const ELEMENT_IDS = {
  MAIN_CONTENT: '__luc_main_content',
  APP_LOADING: '__app_loading__',
} as const
