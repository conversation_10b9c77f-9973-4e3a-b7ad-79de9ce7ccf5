/**
 * 认证状态管理
 */

import type { Recordable, UserInfo } from '@/types/global'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import { 
  loginApi, 
  getAccessToken<PERSON>pi, 
  getUserInfoApi, 
  getAccessC<PERSON><PERSON>pi,
  logoutApi 
} from '@/api'
import { useUserStore } from './user'
import { useAccessStore } from './access'
import { LOGIN_PATH, STORAGE_KEYS } from '@/constants'

// OAuth state key
const PKCE_STATE_KEY = 'pkce_state'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const userStore = useUserStore()
  const accessStore = useAccessStore()
  
  // 状态
  const loginLoading = ref(false)

  // 获取应用配置
  function getAppConfig() {
    return {
      base: import.meta.env.VITE_BASE || '/',
      clientId: import.meta.env.VITE_OAUTH_CLIENT_ID || 'luc-client',
      clientSecret: import.meta.env.VITE_OAUTH_CLIENT_SECRET || 'luc-secret',
      sessionKey: STORAGE_KEYS.SESSION_TOKEN,
    }
  }

  /**
   * 用户名密码登录
   */
  async function authLogin(params: Recordable<any>) {
    console.warn('authLogin 被调用，参数:', params)
    
    try {
      loginLoading.value = true

      // 1. 表单登录，获取会话令牌
      const sessionToken = await loginApi(params)
      console.warn('1、获取到sessionToken：', sessionToken)
      
      if (sessionToken) {
        const { base, clientId } = getAppConfig()
        
        // 2. 构造 OAuth2 授权请求
        const redirectUrl = `${location.origin}${base}oauth2/callback`
        const state = Date.now().toString(36)
        const scope = import.meta.env.VITE_OAUTH_SCOPE || 'read'
        
        // 存储 session 凭证和 state
        sessionStorage.setItem(STORAGE_KEYS.SESSION_TOKEN, sessionToken)
        sessionStorage.setItem(PKCE_STATE_KEY, state)
        
        // 跳转到授权页面
        const authUrl = `/api/auth-server/oauth2/authorize?response_type=code` +
          `&client_id=${encodeURIComponent(clientId)}` +
          `&redirect_uri=${encodeURIComponent(redirectUrl)}` +
          `&state=${encodeURIComponent(state)}` +
          `&scope=${encodeURIComponent(scope)}`
        
        window.location.replace(authUrl)
        return
      } else {
        throw new Error('登录失败，未获取到会话令牌')
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElNotification({
        message: error instanceof Error ? error.message : '登录失败',
        title: '登录失败',
        type: 'error',
      })
    } finally {
      loginLoading.value = false
    }
  }

  /**
   * OAuth2 回调处理
   */
  async function handleOAuth2Callback(code: string, state: string) {
    try {
      const { base, clientId, clientSecret } = getAppConfig()
      
      // 1. 校验 state
      const expectedState = sessionStorage.getItem(PKCE_STATE_KEY)
      if (expectedState && expectedState !== state) {
        throw new Error('state 校验失败')
      }

      // 2. 使用授权码获取访问令牌
      const redirectUri = `${location.origin}${base}oauth2/callback`
      const tokenResult = await getAccessTokenApi({
        code,
        redirect_uri: redirectUri,
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'authorization_code',
      })

      // 3. 存储令牌
      userStore.setAccessToken(tokenResult.access_token)
      accessStore.setAccessToken(tokenResult.access_token)
      
      if (tokenResult.refresh_token) {
        userStore.setRefreshToken(tokenResult.refresh_token)
        accessStore.setRefreshToken(tokenResult.refresh_token)
      }

      // 4. 获取用户信息和权限
      const [userInfo, accessCodes] = await Promise.all([
        getUserInfoApi(),
        getAccessCodesApi(),
      ])

      userStore.setUserInfo(userInfo)
      accessStore.setAccessCodes(accessCodes)

      // 5. 清理临时数据
      sessionStorage.removeItem(PKCE_STATE_KEY)
      sessionStorage.removeItem(STORAGE_KEYS.SESSION_TOKEN)

      // 6. 跳转到目标页面
      const redirect = userInfo.homePath || '/dashboard'
      await router.replace(redirect)

      ElNotification({
        message: `欢迎回来，${userInfo.realName || userInfo.username}！`,
        title: '登录成功',
        type: 'success',
      })

      return { userInfo }
    } catch (error) {
      console.error('OAuth2 回调处理失败:', error)
      throw error
    }
  }

  /**
   * OAuth2 登录
   */
  async function oauth2Login(providerId: string) {
    try {
      loginLoading.value = true
      window.location.href = `/api/auth-server/oauth2/authorization/${providerId}`
    } finally {
      loginLoading.value = false
    }
  }

  /**
   * 退出登录
   */
  async function logout(redirect: boolean = true) {
    try {
      await logoutApi()
    } catch {
      // 忽略退出登录的错误
    }

    // 清理所有状态
    userStore.clearUserInfo()
    accessStore.clearAccess()

    if (redirect) {
      await router.replace({
        path: LOGIN_PATH,
        query: {
          redirect: encodeURIComponent(router.currentRoute.value.fullPath),
        },
      })
    }
  }

  /**
   * 获取用户信息
   */
  async function fetchUserInfo() {
    try {
      const userInfo = await getUserInfoApi()
      userStore.setUserInfo(userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 清除无效的 token
      userStore.setAccessToken(null)
      accessStore.setAccessToken(null)
      throw error
    }
  }

  return {
    // 状态
    loginLoading,
    
    // Actions
    authLogin,
    handleOAuth2Callback,
    oauth2Login,
    logout,
    fetchUserInfo,
  }
})
